<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习进度 - SuXi</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            color: white;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .nav-btn {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .progress-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .progress-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .progress-card:hover {
            transform: translateY(-5px);
        }

        .progress-card h3 {
            color: white;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border-radius: 5px;
            transition: width 0.5s ease;
        }

        .progress-text {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            font-weight: 600;
        }

        .learning-phases {
            margin-bottom: 3rem;
        }

        .phase {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #ff6b6b;
        }

        .phase h3 {
            color: white;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .phase-status {
            font-size: 1.2rem;
        }

        .phase-content {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }

        .task-list {
            list-style: none;
            margin-top: 1rem;
        }

        .task-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            border-radius: 8px;
            transition: background 0.3s ease;
        }

        .task-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .task-checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid rgba(255, 255, 255, 0.5);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .task-checkbox.completed {
            background: #4CAF50;
            border-color: #4CAF50;
        }

        .task-checkbox.completed::after {
            content: '✓';
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .task-text {
            flex: 1;
            color: rgba(255, 255, 255, 0.8);
        }

        .task-text.completed {
            text-decoration: line-through;
            opacity: 0.6;
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1rem;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #ff6b6b;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
                margin: 1rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .nav-buttons {
                flex-direction: column;
                align-items: center;
            }

            .nav-btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 前端学习进度追踪</h1>
            <p>记录你的学习旅程，见证每一步成长</p>
        </div>

        <div class="nav-buttons">
            <a href="index.html" class="nav-btn">🏠 返回首页</a>
            <a href="projects.html" class="nav-btn">🎨 项目展示</a>
            <a href="tools.html" class="nav-btn">🔧 工具箱</a>
        </div>

        <div class="progress-overview">
            <div class="progress-card">
                <h3>总体进度</h3>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 35%"></div>
                </div>
                <div class="progress-text">35% 完成</div>
            </div>
            <div class="progress-card">
                <h3>当前阶段</h3>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 70%"></div>
                </div>
                <div class="progress-text">第3周 - JavaScript深入</div>
            </div>
            <div class="progress-card">
                <h3>学习天数</h3>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 60%"></div>
                </div>
                <div class="progress-text">18 / 90 天</div>
            </div>
        </div>

        <div class="learning-phases">
            <div class="phase">
                <h3>
                    <span class="phase-status">✅</span>
                    第1-2周：HTML、CSS和JavaScript基础复习
                </h3>
                <div class="phase-content">
                    <p>已完成基础知识复习，掌握了HTML语义化标签、CSS布局和JavaScript基础语法。</p>
                    <ul class="task-list">
                        <li class="task-item">
                            <div class="task-checkbox completed"></div>
                            <span class="task-text completed">HTML基本结构和语义化标签</span>
                        </li>
                        <li class="task-item">
                            <div class="task-checkbox completed"></div>
                            <span class="task-text completed">CSS选择器和盒模型</span>
                        </li>
                        <li class="task-item">
                            <div class="task-checkbox completed"></div>
                            <span class="task-text completed">Flexbox布局</span>
                        </li>
                        <li class="task-item">
                            <div class="task-checkbox completed"></div>
                            <span class="task-text completed">JavaScript变量和函数</span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="phase">
                <h3>
                    <span class="phase-status">🔄</span>
                    第3-4周：深入学习JavaScript和DOM操作
                </h3>
                <div class="phase-content">
                    <p>正在学习DOM操作和异步编程，已完成部分内容。</p>
                    <ul class="task-list">
                        <li class="task-item">
                            <div class="task-checkbox completed"></div>
                            <span class="task-text completed">DOM操作基础</span>
                        </li>
                        <li class="task-item">
                            <div class="task-checkbox completed"></div>
                            <span class="task-text completed">事件处理</span>
                        </li>
                        <li class="task-item">
                            <div class="task-checkbox"></div>
                            <span class="task-text">Promise和async/await</span>
                        </li>
                        <li class="task-item">
                            <div class="task-checkbox"></div>
                            <span class="task-text">ES6+新特性</span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="phase">
                <h3>
                    <span class="phase-status">⏳</span>
                    第5-7周：学习React框架
                </h3>
                <div class="phase-content">
                    <p>即将开始React框架的学习，这是前端开发的重要里程碑。</p>
                    <ul class="task-list">
                        <li class="task-item">
                            <div class="task-checkbox"></div>
                            <span class="task-text">React基础和JSX</span>
                        </li>
                        <li class="task-item">
                            <div class="task-checkbox"></div>
                            <span class="task-text">组件和状态管理</span>
                        </li>
                        <li class="task-item">
                            <div class="task-checkbox"></div>
                            <span class="task-text">React Hooks</span>
                        </li>
                        <li class="task-item">
                            <div class="task-checkbox"></div>
                            <span class="task-text">React Router</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="stats-section">
            <h2 style="color: white; margin-bottom: 1rem;">📊 学习统计</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">12</div>
                    <div class="stat-label">已完成任务</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">8</div>
                    <div class="stat-label">进行中任务</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">完成项目</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">45</div>
                    <div class="stat-label">学习小时</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 任务复选框交互
        document.querySelectorAll('.task-checkbox').forEach(checkbox => {
            checkbox.addEventListener('click', function() {
                this.classList.toggle('completed');
                const taskText = this.nextElementSibling;
                taskText.classList.toggle('completed');
                
                // 更新进度
                updateProgress();
            });
        });

        function updateProgress() {
            const totalTasks = document.querySelectorAll('.task-checkbox').length;
            const completedTasks = document.querySelectorAll('.task-checkbox.completed').length;
            const progress = Math.round((completedTasks / totalTasks) * 100);
            
            // 更新总体进度
            const progressFill = document.querySelector('.progress-fill');
            const progressText = document.querySelector('.progress-text');
            
            progressFill.style.width = progress + '%';
            progressText.textContent = progress + '% 完成';
        }

        // 页面加载时初始化进度
        document.addEventListener('DOMContentLoaded', function() {
            updateProgress();
        });
    </script>
</body>
</html>
