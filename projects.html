<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目展示 - SuXi</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            color: white;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .nav-btn {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .filter-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .project-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .project-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .project-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .project-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        }

        .project-content {
            padding: 1.5rem;
        }

        .project-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: white;
            margin-bottom: 0.5rem;
        }

        .project-description {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .project-tags {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .tag {
            padding: 4px 8px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border-radius: 12px;
            font-size: 0.8rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .project-links {
            display: flex;
            gap: 1rem;
        }

        .project-link {
            padding: 8px 16px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            text-decoration: none;
            border-radius: 20px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .project-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }

        .project-link.secondary {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .project-link.secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 5px 15px rgba(255, 255, 255, 0.2);
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-top: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1rem;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #ff6b6b;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
                margin: 1rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .projects-grid {
                grid-template-columns: 1fr;
            }

            .nav-buttons,
            .filter-buttons {
                flex-direction: column;
                align-items: center;
            }

            .nav-btn,
            .filter-btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 项目展示</h1>
            <p>记录学习路上的每一个作品</p>
        </div>

        <div class="nav-buttons">
            <a href="index.html" class="nav-btn">🏠 返回首页</a>
            <a href="learning-progress.html" class="nav-btn">📚 学习进度</a>
            <a href="tools.html" class="nav-btn">🔧 工具箱</a>
        </div>

        <div class="filter-buttons">
            <button class="filter-btn active" data-filter="all">全部</button>
            <button class="filter-btn" data-filter="html-css">HTML/CSS</button>
            <button class="filter-btn" data-filter="javascript">JavaScript</button>
            <button class="filter-btn" data-filter="react">React</button>
            <button class="filter-btn" data-filter="practice">练习项目</button>
        </div>

        <div class="projects-grid">
            <div class="project-card" data-category="html-css practice">
                <div class="project-image">🌸</div>
                <div class="project-content">
                    <h3 class="project-title">苏溪欢迎页面</h3>
                    <p class="project-description">
                        一个现代化的欢迎界面，包含动画效果、响应式设计和交互功能。使用了CSS3动画、毛玻璃效果和JavaScript交互。
                    </p>
                    <div class="project-tags">
                        <span class="tag">HTML5</span>
                        <span class="tag">CSS3</span>
                        <span class="tag">JavaScript</span>
                        <span class="tag">响应式</span>
                    </div>
                    <div class="project-links">
                        <a href="index.html" class="project-link">查看项目</a>
                        <button class="project-link secondary" onclick="showProjectDetails('welcome')">详情</button>
                    </div>
                </div>
            </div>

            <div class="project-card" data-category="html-css practice">
                <div class="project-image">📝</div>
                <div class="project-content">
                    <h3 class="project-title">HTML综合练习</h3>
                    <p class="project-description">
                        包含各种HTML元素的综合练习页面，涵盖表单、多媒体、表格等元素，展示了HTML的语义化使用。
                    </p>
                    <div class="project-tags">
                        <span class="tag">HTML5</span>
                        <span class="tag">表单</span>
                        <span class="tag">多媒体</span>
                        <span class="tag">语义化</span>
                    </div>
                    <div class="project-links">
                        <button class="project-link" onclick="createHTMLExercise()">查看项目</button>
                        <button class="project-link secondary" onclick="showProjectDetails('html-exercise')">详情</button>
                    </div>
                </div>
            </div>

            <div class="project-card" data-category="javascript practice">
                <div class="project-image">📊</div>
                <div class="project-content">
                    <h3 class="project-title">学习进度追踪器</h3>
                    <p class="project-description">
                        交互式的学习进度管理工具，可以追踪学习任务完成情况，实时更新进度统计。
                    </p>
                    <div class="project-tags">
                        <span class="tag">JavaScript</span>
                        <span class="tag">DOM操作</span>
                        <span class="tag">数据可视化</span>
                        <span class="tag">交互设计</span>
                    </div>
                    <div class="project-links">
                        <a href="learning-progress.html" class="project-link">查看项目</a>
                        <button class="project-link secondary" onclick="showProjectDetails('progress')">详情</button>
                    </div>
                </div>
            </div>

            <div class="project-card" data-category="javascript">
                <div class="project-image">🎮</div>
                <div class="project-content">
                    <h3 class="project-title">待办事项应用</h3>
                    <p class="project-description">
                        即将开发的React待办事项应用，将包含任务管理、分类、搜索等功能。
                    </p>
                    <div class="project-tags">
                        <span class="tag">计划中</span>
                        <span class="tag">React</span>
                        <span class="tag">状态管理</span>
                        <span class="tag">本地存储</span>
                    </div>
                    <div class="project-links">
                        <button class="project-link" disabled>开发中</button>
                        <button class="project-link secondary" onclick="showProjectDetails('todo')">详情</button>
                    </div>
                </div>
            </div>

            <div class="project-card" data-category="react">
                <div class="project-image">📱</div>
                <div class="project-content">
                    <h3 class="project-title">个人博客网站</h3>
                    <p class="project-description">
                        使用React构建的个人博客，将包含文章管理、评论系统、标签分类等功能。
                    </p>
                    <div class="project-tags">
                        <span class="tag">计划中</span>
                        <span class="tag">React</span>
                        <span class="tag">Router</span>
                        <span class="tag">博客系统</span>
                    </div>
                    <div class="project-links">
                        <button class="project-link" disabled>规划中</button>
                        <button class="project-link secondary" onclick="showProjectDetails('blog')">详情</button>
                    </div>
                </div>
            </div>

            <div class="project-card" data-category="react">
                <div class="project-image">🛒</div>
                <div class="project-content">
                    <h3 class="project-title">电商产品页面</h3>
                    <p class="project-description">
                        电商产品展示页面，包含产品详情、购物车、用户评价等功能模块。
                    </p>
                    <div class="project-tags">
                        <span class="tag">计划中</span>
                        <span class="tag">React</span>
                        <span class="tag">电商</span>
                        <span class="tag">组件化</span>
                    </div>
                    <div class="project-links">
                        <button class="project-link" disabled>规划中</button>
                        <button class="project-link secondary" onclick="showProjectDetails('ecommerce')">详情</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="stats-section">
            <h2 style="color: white; margin-bottom: 1rem;">📈 项目统计</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">已完成项目</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">1</div>
                    <div class="stat-label">进行中项目</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">2</div>
                    <div class="stat-label">计划中项目</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">8</div>
                    <div class="stat-label">使用技术</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 项目筛选功能
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 更新按钮状态
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                const filter = this.dataset.filter;
                const projects = document.querySelectorAll('.project-card');
                
                projects.forEach(project => {
                    if (filter === 'all' || project.dataset.category.includes(filter)) {
                        project.style.display = 'block';
                        project.style.animation = 'fadeIn 0.5s ease';
                    } else {
                        project.style.display = 'none';
                    }
                });
            });
        });

        // 项目详情显示
        function showProjectDetails(projectId) {
            const details = {
                'welcome': '这是一个现代化的欢迎界面项目，展示了CSS3动画、毛玻璃效果、响应式设计等技术。包含了粒子动画、实时时间显示、交互按钮等功能。',
                'html-exercise': 'HTML综合练习项目，涵盖了HTML5的各种元素使用，包括语义化标签、表单元素、多媒体元素等，是学习HTML基础的重要练习。',
                'progress': '学习进度追踪器使用JavaScript实现了动态的进度管理功能，可以实时更新学习状态，统计完成情况，是DOM操作和数据处理的良好实践。',
                'todo': '计划开发的React待办事项应用，将实现任务的增删改查、分类管理、搜索筛选等功能，是学习React状态管理的重要项目。',
                'blog': '个人博客网站将使用React Router实现多页面导航，包含文章列表、详情页、标签系统等功能，是全栈开发的重要练习。',
                'ecommerce': '电商产品页面将实现产品展示、购物车、用户交互等电商核心功能，是React组件化开发的综合实践。'
            };
            
            alert('📋 项目详情\n\n' + details[projectId]);
        }

        // 创建HTML练习页面
        function createHTMLExercise() {
            alert('🚀 即将跳转到HTML综合练习页面\n\n这个页面展示了HTML的各种元素使用方法，包括表单、多媒体、表格等内容。');
            // 这里可以添加实际的页面跳转逻辑
        }

        // 添加淡入动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(20px); }
                to { opacity: 1; transform: translateY(0); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
